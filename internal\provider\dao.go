package provider

import (
	"marketing/internal/config"
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	"marketing/internal/dao/app_system"
	"marketing/internal/dao/auth"
	"marketing/internal/dao/endpoint"
	"marketing/internal/dao/prototype"
	"marketing/internal/dao/rbcare_data"
	"marketing/internal/dao/warranty"

	"github.com/google/wire"
)

type DaoProvider struct {
	UserDao                 admin_user.UserDao
	UserCache               admin_user.UserCacheInterface
	AuthCache               auth.AuthCacheInterface
	ResourceGroupRepository dao.ResourceGroupRepository
}

func NewDaoProvider(cfg *config.Config, infra *InfrastructureProvider, userDao admin_user.UserDao) *DaoProvider {
	return &DaoProvider{
		UserDao:                 userDao,
		UserCache:               admin_user.NewUserCache(infra.Redis),
		AuthCache:               auth.NewAuthCache(infra.Redis),
		ResourceGroupRepository: dao.NewResourceGroupRepository(infra.DBs[cfg.MySQL.Default]),
	}
}

func NewUserDao(cfg *config.Config, infra *InfrastructureProvider) admin_user.UserDao {
	return admin_user.NewUserDao(infra.DBs[cfg.MySQL.Default])
}

// NewAcDevicesUniqDao 从rbcare数据库创建AcDevicesUniqDao
func NewAcDevicesUniqDao(cfg *config.Config, infra *InfrastructureProvider) rbcare_data.AcDevicesUniqDao {
	return rbcare_data.NewAcDevicesUniqDao(infra.DBs[cfg.MySQL.RbcareData])
}

var DaoSet = wire.NewSet(
	admin_user.NewAdminUserLog,
	NewUserDao,
	endpoint.NewEndpointDao,
	endpoint.NewEndpointImageDao,
	endpoint.NewEndpointInfoApplyDao,
	endpoint.NewSettingDao,
	app_system.NewAppSystem,
	app_system.NewAppSystemCache,
	prototype.NewPrototypeDao,
	prototype.NewPrototypeCache,
	warranty.NewWarrantyDao,

	// 题库相关DAO
	dao.NewAppNotificationTabletUpdateDao,
	dao.NewAppUpdateHistoryDao,
	dao.NewEndpointNoticeDao,
	dao.NewAppNotificationTypeDao,

	NewAcDevicesUniqDao,

	NewDaoProvider,
)
