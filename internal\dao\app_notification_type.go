package dao

import (
	"errors"
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AppNotificationTypeDao interface {
	GetAppNotificationTypeList(c *gin.Context) ([]*model.AppNotificationType, error)
	GetAppNotificationTypeByID(c *gin.Context, id int) (*model.AppNotificationType, error)
	GetAppNotificationTypeBySlug(c *gin.Context, slug string) (*model.AppNotificationType, error)
	CreateAppNotificationType(c *gin.Context, notificationType *model.AppNotificationType) error
	UpdateAppNotificationType(c *gin.Context, id int, updateMap map[string]interface{}) error
	DeleteAppNotificationType(c *gin.Context, id int) error
}

type appNotificationTypeDao struct {
	db *gorm.DB
}

func NewAppNotificationTypeDao(db *gorm.DB) AppNotificationTypeDao {
	return &appNotificationTypeDao{
		db: db,
	}
}

// GetAppNotificationTypeList 获取推送消息类型列表
func (d *appNotificationTypeDao) GetAppNotificationTypeList(c *gin.Context) ([]*model.AppNotificationType, error) {
	var notificationTypes []*model.AppNotificationType
	err := d.db.WithContext(c).Order("id ASC").Find(&notificationTypes).Error
	if err != nil {
		return nil, err
	}
	return notificationTypes, nil
}

// GetAppNotificationTypeByID 根据ID获取推送消息类型
func (d *appNotificationTypeDao) GetAppNotificationTypeByID(c *gin.Context, id int) (*model.AppNotificationType, error) {
	var notificationType model.AppNotificationType
	err := d.db.WithContext(c).Where("id = ?", id).First(&notificationType).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &notificationType, nil
}

// GetAppNotificationTypeBySlug 根据Slug获取推送消息类型
func (d *appNotificationTypeDao) GetAppNotificationTypeBySlug(c *gin.Context, slug string) (*model.AppNotificationType, error) {
	var notificationType model.AppNotificationType
	err := d.db.WithContext(c).Where("slug = ?", slug).First(&notificationType).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &notificationType, nil
}

// CreateAppNotificationType 创建推送消息类型
func (d *appNotificationTypeDao) CreateAppNotificationType(c *gin.Context, notificationType *model.AppNotificationType) error {
	return d.db.WithContext(c).Create(notificationType).Error
}

// UpdateAppNotificationType 更新推送消息类型
func (d *appNotificationTypeDao) UpdateAppNotificationType(c *gin.Context, id int, updateMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.AppNotificationType{}).Where("id = ?", id).Updates(updateMap).Error
}

// DeleteAppNotificationType 删除推送消息类型
func (d *appNotificationTypeDao) DeleteAppNotificationType(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.AppNotificationType{}, "id = ?", id).Error
}
