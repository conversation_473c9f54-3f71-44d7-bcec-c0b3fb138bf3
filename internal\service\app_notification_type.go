package service

import (
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"time"

	"github.com/gin-gonic/gin"
)

type AppNotificationTypeSvc interface {
	GetAppNotificationTypeList(c *gin.Context) ([]*model.AppNotificationType, error)
	GetAppNotificationTypeByID(c *gin.Context, id int) (*model.AppNotificationType, error)
	GetAppNotificationTypeDetail(c *gin.Context, slug string) (*AppNotificationTypeDetailResp, error)
	CreateAppNotificationType(c *gin.Context, req *CreateAppNotificationTypeReq) (int, error)
	UpdateAppNotificationType(c *gin.Context, id int, req *UpdateAppNotificationTypeReq) error
	DeleteAppNotificationType(c *gin.Context, id int) error
}

type appNotificationTypeSvc struct {
	dao dao.AppNotificationTypeDao
}

func NewAppNotificationTypeSvc(dao dao.AppNotificationTypeDao) AppNotificationTypeSvc {
	return &appNotificationTypeSvc{
		dao: dao,
	}
}

// GetAppNotificationTypeList 获取推送消息类型列表
func (s *appNotificationTypeSvc) GetAppNotificationTypeList(c *gin.Context) ([]*model.AppNotificationType, error) {
	return s.dao.GetAppNotificationTypeList(c)
}

// GetAppNotificationTypeByID 根据ID获取推送消息类型
func (s *appNotificationTypeSvc) GetAppNotificationTypeByID(c *gin.Context, id int) (*model.AppNotificationType, error) {
	return s.dao.GetAppNotificationTypeByID(c, id)
}

// GetAppNotificationTypeDetail 根据slug获取推送消息类型详情
func (s *appNotificationTypeSvc) GetAppNotificationTypeDetail(c *gin.Context, slug string) (*AppNotificationTypeDetailResp, error) {
	notificationType, err := s.dao.GetAppNotificationTypeBySlug(c, slug)
	if err != nil {
		return nil, err
	}
	if notificationType == nil {
		return nil, errors.NewErr("消息类型不存在")
	}

	return &AppNotificationTypeDetailResp{
		Action:     notificationType.Action,
		URL:        notificationType.URL,
		Popup:      notificationType.Popup,
		ActionText: notificationType.ActionText,
		Banner:     notificationType.Banner,
	}, nil
}

// CreateAppNotificationType 创建推送消息类型
func (s *appNotificationTypeSvc) CreateAppNotificationType(c *gin.Context, req *CreateAppNotificationTypeReq) (int, error) {
	if err := s.validateCreateRequest(req); err != nil {
		return 0, err
	}

	// 检查slug是否已存在
	existing, err := s.dao.GetAppNotificationTypeBySlug(c, req.Slug)
	if err != nil {
		return 0, err
	}
	if existing != nil {
		return 0, errors.NewErr("标识已存在")
	}

	notificationType := &model.AppNotificationType{
		Name:       req.Name,
		Icon:       req.Icon,
		Slug:       req.Slug,
		MsgType:    req.MsgType,
		MediaURL:   req.MediaURL,
		Action:     req.Action,
		URL:        req.URL,
		ActionText: req.ActionText,
		Popup:      req.Popup,
		Banner:     req.Banner,
		Manual:     req.Manual,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err = s.dao.CreateAppNotificationType(c, notificationType)
	if err != nil {
		return 0, err
	}

	return notificationType.ID, nil
}

// UpdateAppNotificationType 更新推送消息类型
func (s *appNotificationTypeSvc) UpdateAppNotificationType(c *gin.Context, id int, req *UpdateAppNotificationTypeReq) error {
	if err := s.validateUpdateRequest(req); err != nil {
		return err
	}

	// 检查消息类型是否存在
	notificationType, err := s.dao.GetAppNotificationTypeByID(c, id)
	if err != nil {
		return err
	}
	if notificationType == nil {
		return errors.NewErr("消息类型不存在")
	}

	updateMap := map[string]interface{}{
		"name":        req.Name,
		"icon":        req.Icon,
		"msg_type":    req.MsgType,
		"media_url":   req.MediaURL,
		"action":      req.Action,
		"url":         req.URL,
		"action_text": req.ActionText,
		"popup":       req.Popup,
		"banner":      req.Banner,
		"manual":      req.Manual,
		"updated_at":  time.Now(),
	}

	return s.dao.UpdateAppNotificationType(c, id, updateMap)
}

// DeleteAppNotificationType 删除推送消息类型
func (s *appNotificationTypeSvc) DeleteAppNotificationType(c *gin.Context, id int) error {
	// 检查消息类型是否存在
	notificationType, err := s.dao.GetAppNotificationTypeByID(c, id)
	if err != nil {
		return err
	}
	if notificationType == nil {
		return errors.NewErr("消息类型不存在")
	}

	return s.dao.DeleteAppNotificationType(c, id)
}

// validateCreateRequest 验证创建请求
func (s *appNotificationTypeSvc) validateCreateRequest(req *CreateAppNotificationTypeReq) error {
	if req.Name == "" {
		return errors.NewErr("名称不能为空")
	}
	if req.Icon == "" {
		return errors.NewErr("图标不能为空")
	}
	if req.Slug == "" {
		return errors.NewErr("标识不能为空")
	}
	if req.MsgType != "text" && req.MsgType != "image" {
		return errors.NewErr("消息类型必须是text或image")
	}
	if req.MsgType == "image" && req.MediaURL == "" {
		return errors.NewErr("图文消息必须填写媒体资源地址")
	}
	if req.Action != "none" && req.Action != "forward" && req.Action != "check_app_upgrade" {
		return errors.NewErr("消息动作必须是none、forward或check_app_upgrade")
	}
	if req.Action == "forward" && req.URL == "" {
		return errors.NewErr("消息动作为跳转时必须填写跳转地址")
	}
	if req.Action != "none" && req.ActionText == "" {
		return errors.NewErr("点击消息有动作时必须填写动作提示文字")
	}
	return nil
}

// validateUpdateRequest 验证更新请求
func (s *appNotificationTypeSvc) validateUpdateRequest(req *UpdateAppNotificationTypeReq) error {
	if req.Name == "" {
		return errors.NewErr("名称不能为空")
	}
	if req.Icon == "" {
		return errors.NewErr("图标不能为空")
	}
	if req.MsgType != "text" && req.MsgType != "image" {
		return errors.NewErr("消息类型必须是text或image")
	}
	if req.MsgType == "image" && req.MediaURL == "" {
		return errors.NewErr("图文消息必须填写媒体资源地址")
	}
	if req.Action != "none" && req.Action != "forward" && req.Action != "check_app_upgrade" {
		return errors.NewErr("消息动作必须是none、forward或check_app_upgrade")
	}
	if req.Action == "forward" && req.URL == "" {
		return errors.NewErr("消息动作为跳转时必须填写跳转地址")
	}
	if req.Action != "none" && req.ActionText == "" {
		return errors.NewErr("点击消息有动作时必须填写动作提示文字")
	}
	return nil
}

// CreateAppNotificationTypeReq 创建推送消息类型请求
type CreateAppNotificationTypeReq struct {
	Name       string `json:"name" binding:"required"`
	Icon       string `json:"icon" binding:"required"`
	Slug       string `json:"slug" binding:"required"`
	MsgType    string `json:"msg_type" binding:"required"`
	MediaURL   string `json:"media_url"`
	Action     string `json:"action" binding:"required"`
	URL        string `json:"url"`
	ActionText string `json:"action_text"`
	Popup      int8   `json:"popup"`
	Banner     int8   `json:"banner"`
	Manual     int8   `json:"manual"`
}

// UpdateAppNotificationTypeReq 更新推送消息类型请求
type UpdateAppNotificationTypeReq struct {
	Name       string `json:"name" binding:"required"`
	Icon       string `json:"icon" binding:"required"`
	MsgType    string `json:"msg_type" binding:"required"`
	MediaURL   string `json:"media_url"`
	Action     string `json:"action" binding:"required"`
	URL        string `json:"url"`
	ActionText string `json:"action_text"`
	Popup      int8   `json:"popup"`
	Banner     int8   `json:"banner"`
	Manual     int8   `json:"manual"`
}

// AppNotificationTypeDetailResp 推送消息类型详情响应
type AppNotificationTypeDetailResp struct {
	Action     string `json:"action"`
	URL        string `json:"url"`
	Popup      int8   `json:"popup"`
	ActionText string `json:"action_text"`
	Banner     int8   `json:"banner"`
}
