package service

import (
	"encoding/json"
	"fmt"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/wecom"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PushNotificationSvc interface {
	GetPushNotificationList(c *gin.Context) ([]*PushNotificationResp, error)
	GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error)
	GetActiveRoles(c *gin.Context) ([]*RoleResp, error)
	GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error)
	GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error)
	SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error)
	PushNotification(c *gin.Context, params *PushNotificationParams) error
	RevokeNotification(c *gin.Context, id int) error
}

type pushNotificationSvc struct {
	db          *gorm.DB
	wecomClient *wecom.Client
	userDao     dao.UserDao
}

func NewPushNotificationSvc(db *gorm.DB, wecomClient *wecom.Client, userDao dao.UserDao) PushNotificationSvc {
	return &pushNotificationSvc{
		db:          db,
		wecomClient: wecomClient,
		userDao:     userDao,
	}
}

// GetPushNotificationList 获取推送消息列表
func (s *pushNotificationSvc) GetPushNotificationList(c *gin.Context) ([]*PushNotificationResp, error) {
	var notifications []*model.AppNotification
	
	// 这里需要实现复杂的查询逻辑，包括关联查询和统计
	// 暂时返回空列表
	result := make([]*PushNotificationResp, 0)
	
	return result, nil
}

// GetNotificationTags 获取通知标签
func (s *pushNotificationSvc) GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error) {
	var tags []*model.WecomTag
	if err := s.db.WithContext(c).Find(&tags).Error; err != nil {
		return nil, errors.NewErr("获取通知标签失败: " + err.Error())
	}

	result := make([]*NotificationTagResp, len(tags))
	for i, tag := range tags {
		result[i] = &NotificationTagResp{
			ID:   tag.ID,
			Text: tag.TagName,
		}
	}

	return result, nil
}

// GetActiveRoles 获取活跃角色
func (s *pushNotificationSvc) GetActiveRoles(c *gin.Context) ([]*RoleResp, error) {
	var roles []*model.AdminRoles
	if err := s.db.WithContext(c).Where("status = ? AND app_type >= ?", 1, 0).Find(&roles).Error; err != nil {
		return nil, errors.NewErr("获取角色列表失败: " + err.Error())
	}

	result := make([]*RoleResp, len(roles))
	for i, role := range roles {
		result[i] = &RoleResp{
			Slug: role.Slug,
			Name: role.Name,
		}
	}

	return result, nil
}

// GetNewestTrains 获取最新培训资源
func (s *pushNotificationSvc) GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error) {
	// 这里需要实现获取培训资源的逻辑
	// 暂时返回空列表
	result := make([]*TrainResp, 0)
	return result, nil
}

// GetNewestNotices 获取最新通知
func (s *pushNotificationSvc) GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error) {
	var notices []*model.EndpointNotice
	if err := s.db.WithContext(c).Order("updated_at DESC").Limit(limit).Find(&notices).Error; err != nil {
		return nil, errors.NewErr("获取通知列表失败: " + err.Error())
	}

	result := make([]*NoticeResp, len(notices))
	for i, notice := range notices {
		result[i] = &NoticeResp{
			ID:    notice.ID,
			Title: notice.Title,
		}
	}

	return result, nil
}

// SearchUsers 搜索用户
func (s *pushNotificationSvc) SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error) {
	offset := (page - 1) * pageSize
	
	var users []*model.AdminUsers
	if err := s.db.WithContext(c).
		Where("status = ? AND username LIKE ?", 1, "%"+search+"%").
		Offset(offset).
		Limit(pageSize).
		Find(&users).Error; err != nil {
		return nil, errors.NewErr("搜索用户失败: " + err.Error())
	}

	result := make([]*UserResp, len(users))
	for i, user := range users {
		result[i] = &UserResp{
			ID:   user.ID,
			Text: user.Username,
		}
	}

	return result, nil
}

// PushNotification 推送通知
func (s *pushNotificationSvc) PushNotification(c *gin.Context, params *PushNotificationParams) error {
	// 获取推送目标用户
	userIDs, err := s.getNotifyUserIDs(c, params.Audience)
	if err != nil {
		return err
	}

	// 构建企微推送内容
	content := s.buildWecomContent(params)
	
	// 推送到企业微信
	err = s.pushToWecom(c, userIDs, content)
	if err != nil {
		return err
	}

	// 保存推送记录到数据库
	err = s.saveNotificationRecord(c, params, userIDs)
	if err != nil {
		return err
	}

	return nil
}

// RevokeNotification 撤回通知
func (s *pushNotificationSvc) RevokeNotification(c *gin.Context, id int) error {
	var notification model.AppNotification
	if err := s.db.WithContext(c).First(&notification, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.NewErr("消息不存在")
		}
		return errors.NewErr("查询消息失败: " + err.Error())
	}

	// 检查是否可以撤回（2天内）
	if time.Since(notification.CreatedAt) > 48*time.Hour {
		return errors.NewErr("推送超过两天的消息不能撤回")
	}

	if notification.Revoked == 1 {
		return nil // 已经撤回
	}

	// 更新撤回状态
	now := time.Now()
	if err := s.db.WithContext(c).Model(&notification).Updates(map[string]interface{}{
		"revoked":    1,
		"revoked_at": &now,
	}).Error; err != nil {
		return errors.NewErr("撤回消息失败: " + err.Error())
	}

	return nil
}

// getNotifyUserIDs 获取通知用户ID列表
func (s *pushNotificationSvc) getNotifyUserIDs(c *gin.Context, audience map[string]interface{}) ([]uint, error) {
	var userIDs []uint

	if all, ok := audience["all"]; ok && all == "all" {
		// 推送给所有用户
		var users []*model.AdminUsers
		if err := s.db.WithContext(c).Where("status = ?", 1).Select("id").Find(&users).Error; err != nil {
			return nil, errors.NewErr("获取用户列表失败: " + err.Error())
		}
		for _, user := range users {
			userIDs = append(userIDs, user.ID)
		}
	} else if tags, ok := audience["tags"].([]string); ok {
		// 根据标签推送
		var tagUserRelations []*model.WecomUserTag
		if err := s.db.WithContext(c).
			Joins("JOIN wecom_tags ON wecom_user_tag.tag_id = wecom_tags.id").
			Where("wecom_tags.tag_name IN ?", tags).
			Select("wecom_user_tag.user_id").
			Find(&tagUserRelations).Error; err != nil {
			return nil, errors.NewErr("根据标签获取用户失败: " + err.Error())
		}
		for _, relation := range tagUserRelations {
			userIDs = append(userIDs, relation.UserID)
		}
	} else if users, ok := audience["users"].([]string); ok {
		// 推送给指定用户
		for _, userIDStr := range users {
			if userID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
				userIDs = append(userIDs, uint(userID))
			}
		}
	} else if roles, ok := audience["roles"].([]string); ok {
		// 根据角色推送
		var userRoles []*model.AdminUserRole
		if err := s.db.WithContext(c).
			Joins("JOIN admin_roles ON admin_user_roles.role_id = admin_roles.id").
			Where("admin_roles.slug IN ?", roles).
			Select("admin_user_roles.user_id").
			Find(&userRoles).Error; err != nil {
			return nil, errors.NewErr("根据角色获取用户失败: " + err.Error())
		}
		for _, userRole := range userRoles {
			userIDs = append(userIDs, userRole.UserID)
		}
	}

	return userIDs, nil
}

// buildWecomContent 构建企微推送内容
func (s *pushNotificationSvc) buildWecomContent(params *PushNotificationParams) string {
	content := fmt.Sprintf("%s\n%s", params.Title, params.Content)
	
	if params.ActionText != "" && params.URL != "" {
		content += fmt.Sprintf("\n\n%s: %s", params.ActionText, params.URL)
	}
	
	return content
}

// pushToWecom 推送到企业微信
func (s *pushNotificationSvc) pushToWecom(c *gin.Context, userIDs []uint, content string) error {
	if len(userIDs) == 0 {
		return nil
	}

	// 获取用户的企微ID
	var users []*model.AdminUsers
	if err := s.db.WithContext(c).Where("id IN ?", userIDs).Select("qw_userid").Find(&users).Error; err != nil {
		return errors.NewErr("获取用户企微ID失败: " + err.Error())
	}

	var qwUserIDs []string
	for _, user := range users {
		if user.QWUserID != nil && *user.QWUserID != "" {
			qwUserIDs = append(qwUserIDs, *user.QWUserID)
		}
	}

	if len(qwUserIDs) == 0 {
		return errors.NewErr("没有找到有效的企微用户ID")
	}

	// 推送到企业微信
	toUser := strings.Join(qwUserIDs, "|")
	_, err := s.wecomClient.SendTextMessage(toUser, "", "", 1000001, content) // 这里需要配置正确的agentID
	if err != nil {
		return errors.NewErr("推送到企业微信失败: " + err.Error())
	}

	return nil
}

// saveNotificationRecord 保存通知记录
func (s *pushNotificationSvc) saveNotificationRecord(c *gin.Context, params *PushNotificationParams, userIDs []uint) error {
	// 构建通知内容JSON
	contentData := map[string]interface{}{
		"title":   params.Title,
		"content": params.Content,
		"action":  params.Action,
		"url":     params.URL,
	}
	
	contentJSON, err := json.Marshal(contentData)
	if err != nil {
		return errors.NewErr("序列化通知内容失败: " + err.Error())
	}

	audienceJSON, err := json.Marshal(params.Audience)
	if err != nil {
		return errors.NewErr("序列化推送目标失败: " + err.Error())
	}

	platformJSON, err := json.Marshal(params.Platform)
	if err != nil {
		return errors.NewErr("序列化推送平台失败: " + err.Error())
	}

	// 保存通知记录
	notification := &model.AppNotification{
		TypeID:    1, // 这里需要根据TypeID获取实际的ID
		Content:   string(contentJSON),
		Platform:  string(platformJSON),
		Audience:  string(audienceJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.db.WithContext(c).Create(notification).Error; err != nil {
		return errors.NewErr("保存通知记录失败: " + err.Error())
	}

	// 保存用户收件箱记录
	var inboxRecords []*model.AppNotificationInbox
	for _, userID := range userIDs {
		inboxRecords = append(inboxRecords, &model.AppNotificationInbox{
			UserID:         userID,
			NotificationID: notification.ID,
		})
	}

	if len(inboxRecords) > 0 {
		if err := s.db.WithContext(c).CreateInBatches(inboxRecords, 100).Error; err != nil {
			return errors.NewErr("保存收件箱记录失败: " + err.Error())
		}
	}

	return nil
}

// PushNotificationParams 推送通知参数
type PushNotificationParams struct {
	Title           string                 `json:"title"`
	Content         string                 `json:"content"`
	Platform        []string               `json:"platform"`
	Audience        map[string]interface{} `json:"audience"`
	TypeID          string                 `json:"type_id"`
	Action          string                 `json:"action"`
	ActionText      string                 `json:"action_text"`
	URL             string                 `json:"url"`
	Popup           int                    `json:"popup"`
	Banner          int                    `json:"banner"`
	BannerStartTime string                 `json:"banner_start_time"`
	BannerEndTime   string                 `json:"banner_end_time"`
}

// 响应结构体
type PushNotificationResp struct {
	ID       int    `json:"id"`
	TypeName string `json:"type_name"`
	Content  string `json:"content"`
	Platform string `json:"platform"`
	Audience string `json:"audience"`
	Total    int    `json:"total"`
	Fetched  int    `json:"fetched"`
	Read     int    `json:"read"`
	Checked  int    `json:"checked"`
	Revoked  int    `json:"revoked"`
}

type NotificationTagResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}

type RoleResp struct {
	Slug string `json:"slug"`
	Name string `json:"name"`
}

type TrainResp struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type NoticeResp struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
}

type UserResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}
