package provider

import (
	"marketing/internal/config"
	"marketing/internal/dao/admin_user"
	"marketing/internal/dao/app_system"
	"marketing/internal/service"
	"marketing/internal/service/auth"
	"marketing/internal/service/base"
	"marketing/internal/service/endpoint"
	"marketing/internal/service/mkb"
	"marketing/internal/service/report"
	"marketing/internal/service/system"

	"github.com/google/wire"
)

type ServiceProvider struct {

	// Admin 服务
	AdminRoleService       system.AdminRoleInterface
	AdminUserService       system.AdminUserInterface
	AdminPermissionService system.AdminPermissionInterface
	AdminPermGroupService  system.AdminPermGroupInterface
	AdminMenuService       system.AdminMenuInterface
	AdminUserGroupService  system.AdminUserGroupInterface
	WecomTageService       system.WecomTagInterface
	ResourceGroupService   system.ResourceGroupService
	AppSystemService       system.AppSystemSvc
}

func NewServiceProvider(cfg *config.Config,
	infrastructure *InfrastructureProvider,
	daoProvider *DaoProvider,
	adminUserService system.AdminUserInterface,
	wecomSyncService system.WecomSyncService,
	appSystemService system.AppSystemSvc,
	adminUserLog admin_user.AdminUserLog,
	menuService system.AdminMenuInterface) *ServiceProvider {
	return &ServiceProvider{
		// Admin 服务
		AdminRoleService:       system.NewAdminRoleSvc(infrastructure.DBs[cfg.MySQL.Default], daoProvider.UserDao, daoProvider.UserCache),
		AdminUserService:       adminUserService,
		AdminPermissionService: system.NewAdminPermissionSvc(infrastructure.DBs[cfg.MySQL.Default]),
		AdminPermGroupService:  system.NewAdminPermGroupSvc(infrastructure.DBs[cfg.MySQL.Default]),
		AdminMenuService:       menuService,
		AdminUserGroupService:  system.NewAdminUserGroupSvc(infrastructure.DBs[cfg.MySQL.Default], adminUserLog),
		WecomTageService:       system.NewWecomTagSvc(infrastructure.DBs[cfg.MySQL.Default], cfg),
		ResourceGroupService:   system.NewResourceGroupSvc(daoProvider.ResourceGroupRepository, wecomSyncService),
		AppSystemService:       appSystemService,
	}
}

var ServiceSet = wire.NewSet(
	system.NewAdminMenuSvc,
	base.NewCaptchaService,
	//system
	system.NewAppSystemSvc,

	NewAuthService,

	// 企微同步service
	system.NewSyncService,
	NewAdminUserService,
	report.NewLearningRoomService,

	//app
	endpoint.NewEndpoint, // 添加 endpoint service provider
	endpoint.NewEndpointImageService,
	base.NewRegionService,
	endpoint.NewEndpointInfoApplyService,
	endpoint.NewSettingService,

	mkb.NewMkbService,

	// 题库相关服务
	service.NewTiKuSvc,
	service.NewAppUpdateSvc,
	service.NewEndpointNoticeSvc,
	service.NewAppNotificationTypeSvc,

	NewServiceProvider,
)

func NewAuthService(cfg *config.Config,
	infrastructure *InfrastructureProvider,
	daoProvider *DaoProvider,
	appSystemSvc system.AppSystemSvc,
	appSystemCache app_system.AppSystemCacheInterface,
	menuService system.AdminMenuInterface) auth.ServiceInterface {
	coreAuthService := auth.NewCoreAuthService(infrastructure.DB, daoProvider.UserDao, daoProvider.AuthCache, daoProvider.UserCache, menuService, appSystemCache, appSystemSvc)
	return auth.NewAuthService(cfg, coreAuthService)
}

func NewAdminUserService(cfg *config.Config,
	infrastructure *InfrastructureProvider,
	wecomSyncService system.WecomSyncService,
	appSystem app_system.AppSystem,
	adminUserLog admin_user.AdminUserLog,
	daoProvider *DaoProvider) system.AdminUserInterface {
	return system.NewAdminUserSvc(daoProvider.UserCache,
		infrastructure.DBs[cfg.MySQL.Default],
		daoProvider.UserDao,
		wecomSyncService,
		appSystem,
		adminUserLog,
		daoProvider.AuthCache)
}
