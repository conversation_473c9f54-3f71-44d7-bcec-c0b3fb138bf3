package admin

import (
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	"marketing/internal/handler/admin/notice"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/wecom"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
)

type NoticeRouter struct{}

func NewNoticeRouter() *NoticeRouter {
	return &NoticeRouter{}
}

// Register 注册通知路由
func (e *NoticeRouter) Register(r *gin.RouterGroup) {

	//终端通知管理
	endpointNoticeRouter := r.Group("/endpoint")
	{
		endpointNoticeDao := dao.NewEndpointNoticeDao(db.DB)
		endpointNoticeService := service.NewEndpointNoticeSvc(endpointNoticeDao)
		endpointNoticeHandler := notice.NewEndpointNoticeHandler(endpointNoticeService)

		endpointNoticeRouter.GET("", endpointNoticeHandler.Lists)         // 获取通知列表
		endpointNoticeRouter.GET("/:id", endpointNoticeHandler.Get)       // 获取指定通知
		endpointNoticeRouter.POST("", endpointNoticeHandler.Create)       // 创建通知
		endpointNoticeRouter.PUT("/:id", endpointNoticeHandler.Update)    // 更新通知
		endpointNoticeRouter.DELETE("/:id", endpointNoticeHandler.Delete) // 删除通知
	}

	//推送消息类型管理
	appNotificationTypeRouter := r.Group("/type")
	{
		appNotificationTypeDao := dao.NewAppNotificationTypeDao(db.DB)
		appNotificationTypeService := service.NewAppNotificationTypeSvc(appNotificationTypeDao)
		appNotificationTypeHandler := notice.NewAppNotificationTypeHandler(appNotificationTypeService)

		appNotificationTypeRouter.GET("", appNotificationTypeHandler.Lists)               // 获取推送消息类型列表
		appNotificationTypeRouter.GET("/:id", appNotificationTypeHandler.Get)             // 获取指定推送消息类型
		appNotificationTypeRouter.GET("/detail/:slug", appNotificationTypeHandler.Detail) // 根据slug获取推送消息类型详情
		appNotificationTypeRouter.POST("", appNotificationTypeHandler.Create)             // 创建推送消息类型
		appNotificationTypeRouter.PUT("/:id", appNotificationTypeHandler.Update)          // 更新推送消息类型
		appNotificationTypeRouter.DELETE("/:id", appNotificationTypeHandler.Delete)       // 删除推送消息类型
	}

	//推送消息管理
	pushRouter := r.Group("/push")
	{
		// 创建企微客户端 - 这里需要从配置中获取正确的参数
		wecomClient := wecom.NewWeComClient("your_corp_id", "your_corp_secret") // TODO: 从配置获取
		userDao := admin_user.NewUserDao(db.DB)
		appNotificationTypeDao := dao.NewAppNotificationTypeDao(db.DB)
		appNotificationTypeService := service.NewAppNotificationTypeSvc(appNotificationTypeDao)
		pushService := service.NewPushNotificationSvc(db.DB, wecomClient, userDao)
		pushHandler := notice.NewPushHandler(appNotificationTypeService, pushService)

		pushRouter.GET("", pushHandler.Index)                    // 推送消息列表
		pushRouter.GET("/create", pushHandler.Create)            // 创建推送消息页面
		pushRouter.POST("", pushHandler.Store)                   // 存储推送消息
		pushRouter.GET("/users", pushHandler.Users)              // 搜索用户
		pushRouter.PUT("/:id/revoke", pushHandler.Revoke)        // 撤回推送消息
	}

	//平板更新记录
	tiKuRouter := r.Group("/tablet")
	{
		appNotificationTabletUpdateDao := dao.NewAppNotificationTabletUpdateDao(db.DB)
		appUpdateHistoryDao := dao.NewAppUpdateHistoryDao(db.DB)
		tiKuService := service.NewTiKuSvc(appNotificationTabletUpdateDao)
		appUpdateService := service.NewAppUpdateSvc(appUpdateHistoryDao)
		tiKuHandler := notice.NewTiKuHandler(tiKuService, appUpdateService)

		tiKuRouter.POST("/upload", tiKuHandler.UploadPaperAndHomework)          // 上传试卷和作业本Excel文件
		tiKuRouter.GET("/textbook/options", tiKuHandler.GetTextBookOptions)     // 获取教材类型选项
		tiKuRouter.GET("/textbook/histories", tiKuHandler.GetTextBookHistories) // 获取教材历史记录
		tiKuRouter.GET("/app/options", tiKuHandler.GetAppOptions)               // 获取App选项
		tiKuRouter.GET("/app/histories", tiKuHandler.GetAppUpdateHistories)     // 获取App更新历史
	}
}
