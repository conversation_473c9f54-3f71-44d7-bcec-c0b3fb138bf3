package model

import (
	"time"
)

// AppNotification 推送消息模型
type AppNotification struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	TypeID    int       `json:"type_id" gorm:"not null;column:type_id;comment:消息类型ID"`
	Content   string    `json:"content" gorm:"type:text;not null;column:content;comment:消息内容JSON"`
	Platform  string    `json:"platform" gorm:"type:varchar(100);not null;column:platform;comment:推送平台JSON"`
	Audience  string    `json:"audience" gorm:"type:text;not null;column:audience;comment:推送目标JSON"`
	Revoked   int8      `json:"revoked" gorm:"type:tinyint(1);not null;default:0;column:revoked;comment:是否撤回"`
	RevokedAt *time.Time `json:"revoked_at" gorm:"column:revoked_at;comment:撤回时间"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AppNotification) TableName() string {
	return "app_notification"
}

// AppNotificationInbox 推送消息收件箱模型
type AppNotificationInbox struct {
	ID             uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	UserID         uint      `json:"user_id" gorm:"not null;column:user_id;comment:用户ID"`
	NotificationID uint      `json:"notification_id" gorm:"not null;column:notification_id;comment:消息ID"`
	Fetched        int8      `json:"fetched" gorm:"type:tinyint(1);not null;default:0;column:fetched;comment:是否拉取"`
	Read           int8      `json:"read" gorm:"type:tinyint(1);not null;default:0;column:read;comment:是否浏览"`
	Checked        int8      `json:"checked" gorm:"type:tinyint(1);not null;default:0;column:checked;comment:是否查看"`
	CreatedAt      time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AppNotificationInbox) TableName() string {
	return "app_notification_inbox"
}

// AppNotificationTag 推送消息标签模型
type AppNotificationTag struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Name      string    `json:"name" gorm:"type:varchar(50);not null;column:name;comment:标签名称"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AppNotificationTag) TableName() string {
	return "app_notification_tag"
}
