package model

import (
	"time"
)

// AdminUserRole 用户角色关联模型
type AdminUserRole struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	UserID    uint      `json:"user_id" gorm:"not null;column:user_id;comment:用户ID"`
	RoleID    uint      `json:"role_id" gorm:"not null;column:role_id;comment:角色ID"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (AdminUserRole) TableName() string {
	return "admin_user_roles"
}
